<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VisitorExtraField extends Model
{
    use HasFactory;

    protected $table = 'visitors_extra_fields';

    protected $fillable = [
        'field_key',
        'field_label',
        'field_type',
        'field_options',
        'is_required',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'field_options' => 'array',
        'is_required' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get all active extra fields ordered by sort_order
     */
    public static function getActiveFields()
    {
        return self::where('is_active', true)->orderBy('sort_order')->get();
    }

    /**
     * Get field options as array
     */
    public function getOptionsArray()
    {
        if ($this->field_type === 'select' && $this->field_options) {
            return $this->field_options;
        }
        return [];
    }
}
