<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BadgeField extends Model
{
    use HasFactory;

    protected $fillable = [
        'pdf_template_id',
        'field_name',
        'json_key',
        'x_coordinate',
        'y_coordinate',
        'font_size',
        'max_width',
        'alignment',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the PDF template that owns this field.
     */
    public function pdfTemplate()
    {
        return $this->belongsTo(PdfTemplate::class, 'pdf_template_id');
    }

    /**
     * Get the value for this field from a visitor.
     */
    public function getValueFromVisitor($visitor)
    {
        switch ($this->field_name) {
            case 'name':
                return mb_strtoupper($visitor->prenume . ' ' . $visitor->nume);
            case 'first_name':
                return mb_strtoupper($visitor->prenume);
            case 'last_name':
                return mb_strtoupper($visitor->nume);
            case 'email':
                return $visitor->email;
            case 'region':
                return mb_strtoupper($visitor->regiune ?? '');
            case 'category':
                $category = BadgeCategory::where('name', $visitor->categorie)->first();
                return $category ? mb_strtoupper($category->display_name) : mb_strtoupper($visitor->categorie);
            case 'qr_code':
                // QR code fields don't return text values - they're handled separately in badge generation
                return $visitor->cod ?? '';
            default:
                // Custom field from JSON
                if ($this->json_key && $visitor->date) {
                    $data = is_string($visitor->date) ? json_decode($visitor->date, true) : $visitor->date;
                    return isset($data[$this->json_key]) ? mb_strtoupper($data[$this->json_key]) : '';
                }
                return '';
        }
    }

    /**
     * Get available built-in field options.
     */
    public static function getBuiltInFields()
    {
        return [
            'name' => 'Nume complet',
            'first_name' => 'Prenume',
            'last_name' => 'Nume',
            'email' => 'Email',
            'region' => 'Regiune',
            'category' => 'Categorie',
            'qr_code' => 'Cod QR',
        ];
    }
}
