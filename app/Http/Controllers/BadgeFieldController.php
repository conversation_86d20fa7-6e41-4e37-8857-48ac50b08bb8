<?php

namespace App\Http\Controllers;

use App\Models\BadgeField;
use App\Models\PdfTemplate;
use Illuminate\Http\Request;

class BadgeFieldController extends Controller
{
    /**
     * Display badge fields for a specific template.
     */
    public function index($templateId)
    {
        $template = PdfTemplate::with(['badgeFields', 'category'])->findOrFail($templateId);
        return view('badge-fields.index', compact('template'));
    }

    /**
     * Show the form for creating a new badge field.
     */
    public function create($templateId)
    {
        $template = PdfTemplate::with('category')->findOrFail($templateId);
        $builtInFields = BadgeField::getBuiltInFields();
        return view('badge-fields.create', compact('template', 'builtInFields'));
    }

    /**
     * Store a newly created badge field.
     */
    public function store(Request $request, $templateId)
    {
        $template = PdfTemplate::findOrFail($templateId);

        // Check if this is a QR code field to adjust validation
        $isQrCode = $request->field_name === 'qr_code';

        $validationRules = [
            'field_name' => 'required|string|max:255',
            'json_key' => 'nullable|string|max:255',
            'x_coordinate' => 'required|integer|min:0|max:1000',
            'y_coordinate' => 'required|integer|min:0|max:1000',
            'font_size' => $isQrCode ? 'required|integer|min:10|max:200' : 'required|integer|min:6|max:72',
            'max_width' => 'required|integer|min:0|max:1000',
            'alignment' => $isQrCode ? 'nullable|in:L,C,R' : 'required|in:L,C,R',
        ];

        try {
            $request->validate($validationRules);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Handle validation errors for AJAX requests
            if ($request->expectsJson() || $request->ajax() || $request->wantsJson() || $request->header('Content-Type') === 'application/json') {
                return response()->json([
                    'success' => false,
                    'message' => 'Datele introduse nu sunt valide.',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e; // Re-throw for regular form submissions
        }

        // Get the next sort order
        $maxSortOrder = BadgeField::where('pdf_template_id', $templateId)->max('sort_order') ?? 0;

        // For QR code fields, set appropriate defaults
        $fieldData = [
            'pdf_template_id' => $templateId,
            'field_name' => $request->field_name,
            'json_key' => $request->json_key,
            'x_coordinate' => $request->x_coordinate,
            'y_coordinate' => $request->y_coordinate,
            'font_size' => $request->font_size,
            'max_width' => $isQrCode ? 0 : $request->max_width, // QR codes don't use max_width
            'alignment' => $isQrCode ? 'C' : $request->alignment, // QR codes default to center
            'sort_order' => $maxSortOrder + 1,
        ];

        try {
            BadgeField::create($fieldData);
        } catch (\Exception $e) {
            // Handle database errors for AJAX requests
            if ($request->expectsJson() || $request->ajax() || $request->wantsJson() || $request->header('Content-Type') === 'application/json') {
                return response()->json([
                    'success' => false,
                    'message' => 'Eroare la salvarea câmpului: ' . $e->getMessage()
                ], 500);
            }
            throw $e; // Re-throw for regular form submissions
        }

        // Check if this is an AJAX request (more reliable detection)
        if ($request->expectsJson() || $request->ajax() || $request->wantsJson() || $request->header('Content-Type') === 'application/json') {
            return response()->json([
                'success' => true,
                'message' => 'Câmpul a fost adăugat cu succes.'
            ]);
        }

        return redirect()->route('badge-fields.index', $templateId)
            ->with('success', 'Câmpul a fost adăugat cu succes.');
    }

    /**
     * Show the form for editing a badge field.
     */
    public function edit($templateId, BadgeField $badgeField)
    {
        $template = PdfTemplate::with('category')->findOrFail($templateId);
        $builtInFields = BadgeField::getBuiltInFields();
        
        // Ensure the field belongs to this template
        if ($badgeField->pdf_template_id != $templateId) {
            abort(404);
        }

        return view('badge-fields.edit', compact('template', 'badgeField', 'builtInFields'));
    }

    /**
     * Update a badge field.
     */
    public function update(Request $request, $templateId, BadgeField $badgeField)
    {
        // Ensure the field belongs to this template
        if ($badgeField->pdf_template_id != $templateId) {
            abort(404);
        }

        // Check if this is a QR code field to adjust validation
        $isQrCode = $request->field_name === 'qr_code';

        $validationRules = [
            'field_name' => 'required|string|max:255',
            'json_key' => 'nullable|string|max:255',
            'x_coordinate' => 'required|integer|min:0|max:1000',
            'y_coordinate' => 'required|integer|min:0|max:1000',
            'font_size' => $isQrCode ? 'required|integer|min:10|max:200' : 'required|integer|min:6|max:72',
            'max_width' => 'required|integer|min:0|max:1000',
            'alignment' => $isQrCode ? 'nullable|in:L,C,R' : 'required|in:L,C,R',
            'is_active' => 'boolean',
        ];

        $request->validate($validationRules);

        // For QR code fields, set appropriate defaults
        $updateData = [
            'field_name' => $request->field_name,
            'json_key' => $request->json_key,
            'x_coordinate' => $request->x_coordinate,
            'y_coordinate' => $request->y_coordinate,
            'font_size' => $request->font_size,
            'max_width' => $isQrCode ? 0 : $request->max_width, // QR codes don't use max_width
            'alignment' => $isQrCode ? 'C' : $request->alignment, // QR codes default to center
            'is_active' => $request->has('is_active'),
        ];

        $badgeField->update($updateData);

        return redirect()->route('badge-fields.index', $templateId)
            ->with('success', 'Câmpul a fost actualizat cu succes.');
    }

    /**
     * Remove a badge field.
     */
    public function destroy($templateId, BadgeField $badgeField)
    {
        // Ensure the field belongs to this template
        if ($badgeField->pdf_template_id != $templateId) {
            abort(404);
        }

        $badgeField->delete();

        // Check if this is an AJAX request (more reliable detection)
        if (request()->expectsJson() || request()->ajax() || request()->wantsJson() || request()->header('Content-Type') === 'application/json') {
            return response()->json([
                'success' => true,
                'message' => 'Câmpul a fost șters cu succes.'
            ]);
        }

        return redirect()->route('badge-fields.index', $templateId)
            ->with('success', 'Câmpul a fost șters cu succes.');
    }

    /**
     * Update sort order of fields.
     */
    public function updateOrder(Request $request, $templateId)
    {
        $request->validate([
            'field_ids' => 'required|array',
            'field_ids.*' => 'exists:badge_fields,id',
        ]);

        foreach ($request->field_ids as $index => $fieldId) {
            BadgeField::where('id', $fieldId)
                ->where('pdf_template_id', $templateId)
                ->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Update all fields at once.
     */
    public function updateAll(Request $request, $templateId)
    {
        // Dynamic validation based on field types
        $validationRules = [
            'fields' => 'required|array',
            'fields.*.id' => 'required|exists:badge_fields,id',
            'fields.*.field_name' => 'required|string|max:255',
            'fields.*.json_key' => 'nullable|string|max:255',
            'fields.*.x_coordinate' => 'required|integer|min:0|max:1000',
            'fields.*.y_coordinate' => 'required|integer|min:0|max:1000',
            'fields.*.max_width' => 'required|integer|min:0|max:1000',
            'fields.*.alignment' => 'required|in:L,C,R',
            'fields.*.is_active' => 'boolean',
            'fields.*.sort_order' => 'required|integer|min:1',
        ];

        // Check if any field is QR code to adjust font_size validation
        $hasQrCode = collect($request->fields)->contains('field_name', 'qr_code');
        if ($hasQrCode) {
            $validationRules['fields.*.font_size'] = 'required|integer|min:6|max:200';
        } else {
            $validationRules['fields.*.font_size'] = 'required|integer|min:6|max:72';
        }

        $request->validate($validationRules);

        foreach ($request->fields as $fieldData) {
            $field = BadgeField::where('id', $fieldData['id'])
                ->where('pdf_template_id', $templateId)
                ->first();

            if ($field) {
                $isQrCode = $fieldData['field_name'] === 'qr_code';

                $field->update([
                    'field_name' => $fieldData['field_name'],
                    'json_key' => $fieldData['json_key'],
                    'x_coordinate' => $fieldData['x_coordinate'],
                    'y_coordinate' => $fieldData['y_coordinate'],
                    'font_size' => $fieldData['font_size'],
                    'max_width' => $isQrCode ? 0 : $fieldData['max_width'], // QR codes don't use max_width
                    'alignment' => $isQrCode ? 'C' : $fieldData['alignment'], // QR codes default to center
                    'is_active' => $fieldData['is_active'] ?? false,
                    'sort_order' => $fieldData['sort_order'],
                ]);
            }
        }

        return response()->json(['success' => true]);
    }
}
