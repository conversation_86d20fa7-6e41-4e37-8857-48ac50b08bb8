<?php

namespace App\Http\Controllers;

use App\Models\BadgeCategory;
use App\Models\PdfTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use TCPDI;
use TCPDF_FONTS;

class PdfTemplateController extends Controller
{
    /**
     * Display a listing of PDF templates.
     */
    public function index()
    {
        $templates = PdfTemplate::with('category')->get();
        return view('pdf-templates.index', compact('templates'));
    }

    /**
     * Show the form for creating a new PDF template.
     */
    public function create()
    {
        $categories = BadgeCategory::all();
        return view('pdf-templates.create', compact('categories'));
    }

    /**
     * Store a newly created PDF template in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:badge_categories,id',
            'pdf_file' => 'required|file|mimes:pdf|max:10240', // 10MB max
        ]);

        // Check if category already has a template
        $existingTemplate = PdfTemplate::where('category_id', $request->category_id)->first();
        if ($existingTemplate) {
            return redirect()->back()
                ->with('error', 'Categoria selectată are deja un template PDF asociat.');
        }

        $file = $request->file('pdf_file');
        $filename = Str::random(40) . '.pdf';
        
        // Create directory if it doesn't exist
        $uploadPath = public_path('assets/pdf_templates');
        if (!file_exists($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Move file to public/assets/pdf_templates
        $file->move($uploadPath, $filename);

        PdfTemplate::create([
            'category_id' => $request->category_id,
            'filename' => $filename,
            'original_name' => $file->getClientOriginalName(),
        ]);

        return redirect()->route('pdf-templates.index')
            ->with('success', 'Template PDF a fost încărcat cu succes.');
    }

    /**
     * Show the form for editing the specified PDF template.
     */
    public function edit(PdfTemplate $pdfTemplate)
    {
        $categories = BadgeCategory::all();
        return view('pdf-templates.edit', compact('pdfTemplate', 'categories'));
    }

    /**
     * Update the specified PDF template in storage.
     */
    public function update(Request $request, PdfTemplate $pdfTemplate)
    {
        $request->validate([
            'category_id' => 'required|exists:badge_categories,id',
            'pdf_file' => 'nullable|file|mimes:pdf|max:10240', // 10MB max
        ]);

        // Check if category already has a template (excluding current one)
        $existingTemplate = PdfTemplate::where('category_id', $request->category_id)
            ->where('id', '!=', $pdfTemplate->id)
            ->first();
        if ($existingTemplate) {
            return redirect()->back()
                ->with('error', 'Categoria selectată are deja un template PDF asociat.');
        }

        $updateData = [
            'category_id' => $request->category_id,
        ];

        // Handle file upload if new file is provided
        if ($request->hasFile('pdf_file')) {
            $file = $request->file('pdf_file');
            $filename = Str::random(40) . '.pdf';
            
            $uploadPath = public_path('assets/pdf_templates');
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Delete old file
            $oldFilePath = $uploadPath . '/' . $pdfTemplate->filename;
            if (file_exists($oldFilePath)) {
                unlink($oldFilePath);
            }

            // Move new file
            $file->move($uploadPath, $filename);

            $updateData['filename'] = $filename;
            $updateData['original_name'] = $file->getClientOriginalName();
        }

        $pdfTemplate->update($updateData);

        return redirect()->route('pdf-templates.index')
            ->with('success', 'Template PDF a fost actualizat cu succes.');
    }

    /**
     * Remove the specified PDF template from storage.
     */
    public function destroy(PdfTemplate $pdfTemplate)
    {
        $filePath = public_path('assets/pdf_templates/' . $pdfTemplate->filename);
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        $pdfTemplate->delete();

        return redirect()->route('pdf-templates.index')
            ->with('success', 'Template PDF a fost șters cu succes.');
    }

    /**
     * Generate a test badge with dummy data to test positioning.
     */
    public function testBadge(PdfTemplate $pdfTemplate)
    {
        if (!$pdfTemplate->fileExists()) {
            return redirect()->back()->with('error', 'Fișierul PDF nu există.');
        }

        // Import required classes
        $pdf = new TCPDI();
        $pdf->setSourceFile($pdfTemplate->full_path);

        $tplIdx = $pdf->importPage(1);
        $size = $pdf->getTemplateSize($tplIdx);
        $pdf->SetPrintHeader(false);
        $pdf->SetPrintFooter(false);

        $pdf->AddPage('P', array($size['w'], $size['h']));
        $pdf->useTemplate($tplIdx, 0, 0);
        $pdf->SetMargins(0, 0, 0, false);
        $pdf->SetAutoPageBreak(false, 0);

        // Add font
        $font = public_path('assets/fonts/Roboto/Roboto-Bold.ttf');
        $fontname = TCPDF_FONTS::addTTFfont($font, 'TrueTypeUnicode', '', 96);

        // Test data
        $testData = [
            'name' => 'JOHN DOE',
            'first_name' => 'JOHN',
            'last_name' => 'DOE',
            'email' => '<EMAIL>',
            'region' => 'BUCHAREST',
            'category' => 'PARTICIPANT',
            'company' => 'ACME CORPORATION',
            'position' => 'MANAGER',
            'phone' => '+40 ***********',
        ];

        // Render all badge fields
        foreach ($pdfTemplate->badgeFields()->where('is_active', true)->get() as $field) {
            $value = $testData[$field->field_name] ?? 'TEST VALUE';

            $pdf->SetTextColor(1, 37, 82); // Default blue color
            $pdf->SetFont($fontname, 'B', $field->font_size, '', false);

            $width = $field->max_width > 0 ? $field->max_width : $size['w'];
            $x = $field->x_coordinate;

            // Center horizontally if x_coordinate is 0
            if ($field->x_coordinate == 0) {
                $x = ($size['w'] - $width) / 2;
            }

            $pdf->MultiCell(
                $width,
                0,
                $value,
                0,
                $field->alignment,
                false,
                0,
                $x,
                $field->y_coordinate
            );
        }

        // Add QR code if QR field exists
        $qrField = $pdfTemplate->qrField();
        if ($qrField && $qrField->is_active) {
            // Generate a test QR code
            $qrOptions = new \chillerlan\QRCode\QROptions([
                'outputInterface' => \chillerlan\QRCode\Output\QRGdImageJPEG::class,
                'quietzoneSize' => 2,
                'addQuietzone' => false,
                'imageTransparent' => false,
                'outputBase64' => false,
            ]);

            $qrCode = new \chillerlan\QRCode\QRCode($qrOptions);
            $qrImage = $qrCode->render('TEST-QR-CODE-12345');

            // Apply color transformation
            $image = new \Imagick();
            $image->setSize(500, 500);
            $image->readImageBlob($qrImage);
            $black = new \ImagickPixel('black');
            $blue = new \ImagickPixel('rgb(1,37,82)');
            $image->opaquePaintImage($black, $blue, 0, false);
            $image->setImageFormat('png');
            $qrBlob = $image->getImageBlob();

            $qrSize = $qrField->font_size; // Use font_size as QR size
            $qrX = $qrField->x_coordinate == 0 ?
                ($size['w'] / 2 - $qrSize / 2) :
                $qrField->x_coordinate;

            $pdf->Image(
                '@' . $qrBlob,
                $qrX,
                $qrField->y_coordinate,
                $qrSize,
                $qrSize,
                '',
                '',
                'C',
                false,
                300,
                '',
                false,
                false,
                0
            );
        }

        $pdf->Output('test_badge_' . $pdfTemplate->category->name . '.pdf', "I");
    }
}
