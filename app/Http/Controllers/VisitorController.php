<?php

namespace App\Http\Controllers;

use App\Exports\ScansExport;
use App\Models\BadgeCategory;
use App\Models\BadgeField;
use App\Models\PdfTemplate;
use App\Models\ScanHistory;
use Illuminate\Http\Request;
use App\Models\Vizitator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use chillerlan\QRCode\{Data\QRMatrix, Output\QRGdImageJPEG, QRCode, QROptions};
use Imagick;
use ImagickPixel;
use Maatwebsite\Excel\Facades\Excel;
use TCPDF_FONTS;
use TCPDI;

class VisitorController extends Controller {

	public $rgb = [1, 37, 82];

	// Show all visitors
	public function index() {
//		$json_temp = <<<JSON
//JSON;
//		$visitors = json_decode($json_temp);
//		foreach ($visitors as $_visitor) {
//			$visitor = new Vizitator();
//			$visitor->cod = Str::random(20);
//			$visitor->nume = $_visitor->{'First name'};
//			$visitor->prenume = $_visitor->{'Last Name'};
//			$visitor->email = $_visitor->{'Email'};
//			$visitor->categorie = 'staff';
//			$visitor->date = json_encode($_visitor, JSON_PRETTY_PRINT);
//			$visitor->save();
//		}

		$visitors = Vizitator::all();
		return view('visitors.index', compact('visitors'));
	}

	// Show form to create new visitor
	public function create() {
		return view('visitors.create');
	}

	// Store new visitor in the database
	public function store(Request $request) {
		$request->validate([
			'nume'    => 'required|string|max:255',
			'prenume' => 'required|string|max:255',
		]);

		// if no email, genreate random email
		if (empty($request->input('email'))) {
			$request->merge(['email' => "noemail_" . Str::random(6) . '@example.com']);
		}

		Vizitator::create([
			'cod'       => Str::random(20),
			'nume'      => $request->input('nume'),
			'prenume'   => $request->input('prenume'),
			'email'     => $request->input('email'),
			'categorie' => $request->input('categorie'),
			'date'      => json_encode($request->all(), JSON_PRETTY_PRINT)
		]);

		return redirect()->route('visitors.index')->with('success', 'Participant adăugat cu succes');
	}

	// Show form to edit a visitor
	public function edit($id) {
		$visitor = Vizitator::find($id);
		if (empty($visitor->cod)) {
			$visitor->cod = Str::random(20);
			$visitor->save();
		}
		$img = (new QRCode)->render($visitor->cod);
		return view('visitors.edit', compact('visitor', 'img'));
	}

	// Update existing visitor in the database
	public function update(Request $request, $id) {
		$request->validate([
			'nume'    => 'required|string|max:255',
			'prenume' => 'required|string|max:255',
			'email'   => 'required|email',
		]);

		$visitor = Vizitator::find($id);
		$visitor->update([
			'nume'             => $request->input('nume'),
			'prenume'          => $request->input('prenume'),
			'email'            => $request->input('email'),
			'categorie'        => $request->input('categorie'),
			'regiune'        => $request->input('regiune'),
			'date'             => json_encode($request->all(), JSON_PRETTY_PRINT),
		]);
		return redirect()->route('visitors.edit', $id)->with('success', 'Participant modificat cu succes');
	}

	// Delete a visitor
	public function destroy($id) {
		$visitor = Vizitator::find($id);
		$visitor->delete();

		return redirect()->route('visitors.index')->with('success', 'Participant șters cu succes');
	}

	// Search for a visitor
	public function checkin() {
		$cod     = request('code');
		$visitor = Vizitator::where('cod', $cod)->first();
		if ($visitor) {
			if (auth()->user()->hasRole('expozant')) {
				$scan               = new ScanHistory();
				$scan->id_vizitator = $visitor->id;
				$scan->id_expozant  = auth()->user()->id;
				$scan->save();
				return \response()->json([
					'status'  => 'success',
					'message' => 'Participantul a fost găsit și a fost înregistrat'
				]);
			} else {
				return \response()->json([
					'status'  => 'success',
					'message' => '<div class="text-start">Vizitator: <br>' . '<b>Nume</b>: ' . $visitor->nume . ' ' . $visitor->prenume . '<br>' . '<b>Email</b>: ' . $visitor->email . '<br>' . '<b>Telefon</b>: ' . $visitor->telefon . '<br>' . '<b>Functie</b>: ' . $visitor->functie . '<br>' . '<b>Companie</b>: ' . $visitor->companie_nume . '<br>' . '<b>Adresa</b>: ' . $visitor->companie_adresa . '<br>' . '<b>Oras</b>: ' . $visitor->companie_oras . '<br>' . '<b>Judet</b>: ' . $visitor->companie_judet . '<br>' . '<b>Telefon companie</b>: ' . $visitor->companie_telefon . '<br>' . '<b>Email companie</b>: ' . $visitor->companie_email . '<br></div>'
				]);
			}
		} else {
			return \response()->json(['status' => 'error', 'message' => 'Participantul nu a fost găsit']);
		}
	}

	public function identifyQR() {
		return view('visitors.identify-qr');
	}

	public function identifyQRPost() {
		$cod     = request('code');
		$visitor = Vizitator::where('cod', $cod)->first();
		if ($visitor) {
			// return success with redirect to edit page
			return \response()->json([
				'status'  => 'success',
				'message' => 'Participantul a fost găsit',
				'redirect' => route('visitors.edit', $visitor->id)
			]);
		} else {
			return \response()->json(['status' => 'error', 'message' => 'Participantul nu a fost găsit']);
		}
	}

	public function badge($id) {
		$visitor = Vizitator::find($id);

		try {
			$template = $this->getPdfTemplateForCategory($visitor->categorie);
			$pdf = $this->generateBadgeWithTemplate($visitor, $template, true);
			$pdf->Output('badge_' . $visitor->nume . '.pdf', "I");
		} catch (\Exception $e) {
			return redirect()->back()->with('error', $e->getMessage());
		}
	}

	public function blankBadge($id) {
		$visitor = Vizitator::find($id);

		try {
			$template = $this->getPdfTemplateForCategory($visitor->categorie);
			$pdf = $this->generateBadgeWithTemplate($visitor, $template, false);
			$pdf->Output('blank_badge_' . $visitor->nume . '.pdf', "I");
		} catch (\Exception $e) {
			return redirect()->back()->with('error', $e->getMessage());
		}
	}

	public function badges(Request $request) {
		$categoryName = $request->input('tip', 'participant');
		$visitors = Vizitator::where('categorie', $categoryName)->get();

		if ($visitors->count() == 0) {
			return redirect()->route('visitors.index')->with('error', 'Nu există vizitatori pentru a genera badge-uri');
		}

		try {
			// Get template for this category
			$template = $this->getPdfTemplateForCategory($categoryName);

			$font = public_path('assets/fonts/Roboto/Roboto-Bold.ttf');
			$mainPdf = new TCPDI();

			foreach ($visitors as $visitor) {
				// Generate badge for this visitor
				$pdf = $this->generateBadgeWithTemplate($visitor, $template, true);

				// Import the generated page into main PDF
				$tempFile = tempnam(sys_get_temp_dir(), 'badge_');
				$pdf->Output($tempFile, 'F');

				$mainPdf->setSourceFile($tempFile);
				$tplIdx = $mainPdf->importPage(1);
				$size = $mainPdf->getTemplateSize($tplIdx);

				$mainPdf->SetPrintHeader(false);
				$mainPdf->SetPrintFooter(false);
				$mainPdf->AddPage('P', array($size['w'], $size['h']));
				$mainPdf->useTemplate($tplIdx, 0, 0);

				// Add second page if it exists (back of badge)
				try {
					$backTplIdx = $mainPdf->importPage(2);
					$mainPdf->AddPage('P', array($size['w'], $size['h']));
					$mainPdf->useTemplate($backTplIdx, 0, 0);

					// Add badge index if available
					if ($visitor->badge_index != 0) {
						$fontname = TCPDF_FONTS::addTTFfont($font, 'TrueTypeUnicode', '', 96);
						$mainPdf->SetFont($fontname, 'B', 12, '', false);
						$mainPdf->MultiCell($size['w'], 0, $visitor->badge_index, 0, 'C', false, 0, 0, $size['h'] - 10);
					}
				} catch (Exception $e) {
					// No second page, continue
				}

				unlink($tempFile);
			}

			$mainPdf->Output('badges_' . $categoryName . '.pdf', "I");
		} catch (\Exception $e) {
			return redirect()->route('visitors.index')->with('error', $e->getMessage());
		}
	}

	/**
	 * Show badge generation options with category selection
	 */
	public function badgeOptions() {
		$categories = BadgeCategory::all();
		return view('visitors.badge-options', compact('categories'));
	}

	/**
	 * Generate badge with category selection
	 */
	public function generateBadgeWithCategory(Request $request, $id) {
		$request->validate([
			'category' => 'required|exists:badge_categories,name',
		]);

		$visitor = Vizitator::find($id);
		if (!$visitor) {
			return redirect()->back()->with('error', 'Vizitator nu a fost găsit.');
		}

		try {
			$template = $this->getPdfTemplateForCategory($request->category);
			$pdf = $this->generateBadgeWithTemplate($visitor, $template, true);
			$pdf->Output('badge_' . $visitor->nume . '_' . $request->category . '.pdf', "I");
		} catch (\Exception $e) {
			return redirect()->back()->with('error', $e->getMessage());
		}
	}

	/**
	 * Generate blank badge with category selection
	 */
	public function generateBlankBadgeWithCategory(Request $request, $id) {
		$request->validate([
			'category' => 'required|exists:badge_categories,name',
		]);

		$visitor = Vizitator::find($id);
		if (!$visitor) {
			return redirect()->back()->with('error', 'Vizitator nu a fost găsit.');
		}

		try {
			$template = $this->getPdfTemplateForCategory($request->category);
			$pdf = $this->generateBadgeWithTemplate($visitor, $template, false);
			$pdf->Output('blank_badge_' . $visitor->nume . '_' . $request->category . '.pdf', "I");
		} catch (\Exception $e) {
			return redirect()->back()->with('error', $e->getMessage());
		}
	}

	public function customBadge(Request $request) {
		$id      = $request->input('id_vizitator');
		$visitor = Vizitator::find($id);

		$font        = public_path('assets/fonts/Roboto/Roboto-Bold.ttf');
		$name_coords = array('x' => 0, 'y' => 56);
		$regiune_coords = array('x' => 0, 'y' => 76);
		$qr_cords = array('x' => 33, 'y' => 85);
		$vis_coords  = array('x' => 0, 'y' => 115);

		$pdf      = new TCPDI();

		$pdf->setSourceFile(public_path('assets/badge_participant.pdf'));

		if ($request->input('qr') != 'skip') {
			$img_blob = $this->getQRCode($visitor->cod);
		}

		$tplIdx   = $pdf->importPage(1, '/MediaBox');
		$fontname = TCPDF_FONTS::addTTFfont($font, 'TrueTypeUnicode', '', 96);
		$size     = $pdf->getTemplateSize($tplIdx);
		$pdf->SetPrintHeader(false);
		$pdf->SetPrintFooter(false);

		$pdf->AddPage('P', array($size['w'], $size['h']));
//		$pdf->useTemplate($tplIdx, 0, 0);
		$pdf->SetMargins(0, 0, 0, false);
		$pdf->SetAutoPageBreak(false, 0);

		$pdf->setCellHeightRatio(1);
		// nume
		$pdf->SetTextColor($this->rgb[0], $this->rgb[1], $this->rgb[2]);
		$pdf->SetFont($fontname, 'B', $request->input('font_size_nume') ?? 20, '', false);
		$pdf->MultiCell($size['w'], 20, mb_strtoupper($request->input('prenume')) . " " . mb_strtoupper($request->input('nume')), 0, 'C', false, 0, $name_coords['x'], $name_coords['y'], true, 0, false, false, 20, 'M');

		// oras
		$pdf->SetTextColor($this->rgb[0], $this->rgb[1], $this->rgb[2]);
		$pdf->SetFont($fontname, 'B', $request->input('font_size_regiune') ?? 12, '', false);
		$pdf->MultiCell($size['w'], 0, mb_strtoupper($request->input('regiune')), 0, 'C', false, 0, $regiune_coords['x'], $regiune_coords['y']);

		// label
//		$pdf->SetFont($fontname, 'B', $request->input('font_size_label') ?? 16, '', false);
//		$pdf->MultiCell($size['w'], 0, $request->input('label'), 0, 'C', false, 0, $vis_coords['x'], $vis_coords['y']);

		if ($request->input('qr') != 'skip') {
			// qr code
			$pdf->Image('@' . $img_blob, $size['w'] / 2 - 15, $qr_cords['y'], 30, 30, '', '', '', false, 300, '', false, false, 0);
		}

		// if logo file is uploaded, also add it without saving
		if ($request->hasFile('logo')) {
			$logo = $request->file('logo');
			$logo->move(public_path('temp'), $logo->getClientOriginalName());

			// maximum size of the image is 25 x 45

			$logo_size = getimagesize(public_path('temp/' . $logo->getClientOriginalName()));
			$logo_width = $logo_size[0];
			$logo_height = $logo_size[1];
			$maxWidth = 42; // 45mm
			$maxHeight = 25; // 25mm
			if ($logo_width / $logo_height > $maxWidth / $maxHeight) {
				$logo_width = $maxWidth;
				$logo_height = $maxWidth / $logo_size[0] * $logo_size[1];
			} else {
				$logo_height = $maxHeight;
				$logo_width = $maxHeight / $logo_size[1] * $logo_size[0];
			}

			$pdf->Image(public_path('temp/' . $logo->getClientOriginalName()), $qr_cords['x'] + 30, $qr_cords['y'] + ((25 - $logo_height) / 2), $logo_width, $logo_height, '', '', '', false, 300, '', false, false, 0);
			// delete the file after adding it to the pdf
			unlink(public_path('temp/' . $logo->getClientOriginalName()));
		}

		$pdf->Output('badge_' . $request->input('nume') . '.pdf', "I");
	}

	/**
	 * Get PDF template for a category
	 */
	private function getPdfTemplateForCategory($categoryName) {
		$category = BadgeCategory::where('name', $categoryName)->first();
		if (!$category) {
			throw new \Exception("Categoria '{$categoryName}' nu există.");
		}

		$pdfTemplate = $category->pdfTemplate;
		if (!$pdfTemplate) {
			throw new \Exception("Nu există template PDF pentru categoria '{$category->display_name}'.");
		}

		if (!$pdfTemplate->fileExists()) {
			throw new \Exception("Fișierul PDF pentru categoria '{$category->display_name}' nu există.");
		}

		return $pdfTemplate;
	}

	/**
	 * Generate badge using dynamic field system
	 */
	private function generateBadgeWithTemplate($visitor, $template, $useTemplate = true) {
		$font = public_path('assets/fonts/Roboto/Roboto-Bold.ttf');
		$pdf = new TCPDI();

		$pdf->setSourceFile($template->full_path);

		if (empty($visitor->cod)) {
			$visitor->cod = Str::random(20);
			$visitor->save();
		}

		$img_blob = $this->getQRCode($visitor->cod);
		$tplIdx = $pdf->importPage(1);
		$fontname = TCPDF_FONTS::addTTFfont($font, 'TrueTypeUnicode', '', 96);
		$size = $pdf->getTemplateSize($tplIdx);

		$pdf->SetPrintHeader(false);
		$pdf->SetPrintFooter(false);
		$pdf->AddPage('P', array($size['w'], $size['h']));

		if ($useTemplate) {
			$pdf->useTemplate($tplIdx, 0, 0);
		}

		$pdf->SetMargins(0, 0, 0, false);
		$pdf->SetAutoPageBreak(false, 0);
		$pdf->setCellHeightRatio(1);

		// Render all active badge fields (excluding QR code fields - they are rendered as images, not text)
		foreach ($template->badgeFields()->where('is_active', true)->where('field_name', '!=', 'qr_code')->orderBy('sort_order')->get() as $field) {
			$value = $field->getValueFromVisitor($visitor);

			if (!empty($value)) {
				$pdf->SetTextColor($this->rgb[0], $this->rgb[1], $this->rgb[2]);
				$pdf->SetFont($fontname, 'B', $field->font_size, '', false);

				$width = $field->max_width > 0 ? $field->max_width : $size['w'];
				$x = $field->x_coordinate;

				// Center horizontally if x_coordinate is 0
				if ($field->x_coordinate == 0) {
					$x = ($size['w'] - $width) / 2;
				}

				$pdf->MultiCell(
					$width,
					0,
					$value,
					0,
					$field->alignment,
					false,
					0,
					$x,
					$field->y_coordinate
				);
			}
		}

		// Add QR code if QR field exists
		$qrField = $template->qrField();
		if ($qrField && $qrField->is_active) {
			$qrSize = $qrField->font_size; // Use font_size as QR size
			$qrX = $qrField->x_coordinate == 0 ?
				($size['w'] / 2 - $qrSize / 2) :
				$qrField->x_coordinate;

			$pdf->Image(
				'@' . $img_blob,
				$qrX,
				$qrField->y_coordinate,
				$qrSize,
				$qrSize,
				'',
				'',
				'C',
				false,
				300,
				'',
				false,
				false,
				0
			);
		}

		return $pdf;
	}

	public function getQRCode($cod) {
		$options = new QROptions([
			'outputInterface'  => QRGdImageJPEG::class,
			'quietzoneSize'    => 2,
			'addQuietzone'     => false,
			'imageTransparent' => false,
			'outputBase64'     => false,
		]);

		$img = (new QRCode($options))->render($cod);

		$image = new Imagick();
		$image->setSize(500, 500);
		$image->readImageBlob($img);
		$black = new ImagickPixel('black');
		$blue  = new ImagickPixel('rgb(' . implode(',', $this->rgb) . ')');
		$image->opaquePaintImage($black, $blue, 0, false);
		$image->setImageFormat('png');

		return $image->getImageBlob();
	}

	public function customBadgeView() {
		return view('visitors.badge');
	}

	protected function import_vizitatori() {
		$inscrieri = DB::table('inscrieri')->get();
		foreach ($inscrieri as $inscriere) {
			if ($inscriere->categorie_participant == 1) {
				$visitor      = Vizitator::where('email', $inscriere->expozant_email)->first();
				$visitor->tip = 'expozant';
			} else {
				$visitor      = Vizitator::where('email', $inscriere->email)->first();
				$visitor->tip = 'vizitator';
			}
			$visitor->save();
		}

		return;
	}

	public function updateInscrieri() {
		$inscrieri = DB::connection('formular')->table('inscrieri')->get();

		$olds = 0;
		$news = 0;
		foreach ($inscrieri as $inscriere) {
			$old_vizitator = Vizitator::where('email', $inscriere->email)->orWhere('email', 'LIKE', $inscriere->expozant_email)->first();
			if ($old_vizitator) {
				$olds++;
				continue;
			}
			$_new_vizitator      = new Vizitator();
			$_new_vizitator->cod = Str::random(20);
			if ($inscriere->categorie_participant == 1) {
				$_new_vizitator->nume             = $inscriere->expozant_nume ?? "-";
				$_new_vizitator->prenume          = $inscriere->expozant_prenume ?? "-";
				$_new_vizitator->email            = $inscriere->expozant_email;
				$_new_vizitator->telefon          = "-";
				$_new_vizitator->functie          = "-";
				$_new_vizitator->companie_nume    = $inscriere->expozant_firma;
				$_new_vizitator->companie_telefon = "-";
				$_new_vizitator->companie_email   = $inscriere->expozant_email;
				$_new_vizitator->companie_judet   = "-";
				$_new_vizitator->companie_oras    = "-";
				$_new_vizitator->companie_adresa  = $inscriere->expozant_adresa;
				$_new_vizitator->tip              = 'expozant';
			} else {
				$_new_vizitator->nume             = $inscriere->nume;
				$_new_vizitator->prenume          = $inscriere->prenume;
				$_new_vizitator->email            = $inscriere->email;
				$_new_vizitator->telefon          = $inscriere->telefon;
				$_new_vizitator->functie          = $inscriere->functie;
				$_new_vizitator->companie_nume    = $inscriere->companie_nume;
				$_new_vizitator->companie_telefon = $inscriere->companie_telefon;
				$_new_vizitator->companie_email   = $inscriere->companie_email;
				$_new_vizitator->companie_judet   = $inscriere->companie_judet;
				$_new_vizitator->companie_oras    = $inscriere->companie_oras;
				$_new_vizitator->companie_adresa  = $inscriere->companie_adresa;
				$_new_vizitator->tip              = 'vizitator';
			}
			$_new_vizitator->save();
			$news++;
		}
		dump($olds);
		dd($news);
	}

	public function export(Request $request) {
		$data = new Collection();

		$vizitatori = Vizitator::all();
		$data->push([
			'Nr',
			'Tip',
			'Nume',
			'Prenume',
			'Email',
			'Telefon',
			'A participat?',
		]);

		$i = 1;
		foreach ($vizitatori as $vizitator) {
			$scanare = ScanHistory::where('id_vizitator', $vizitator->id)->first();
//			dd($vizitator->date);
			$data->push([
				'Nr'               => $i++,
				'Tip'              => ucfirst($vizitator->categorie),
				'Nume'             => $vizitator->nume,
				'Prenume'          => $vizitator->prenume,
				'Email'            => $vizitator->email,
				'Telefon'          => $vizitator->date->telefon ?? ($vizitator->date->{'PHONE NUMBER'} ?? '-'),
				'A participat?'    => $scanare ? 'Da' : 'Nu',
			]);
		}

		return Excel::download(new ScansExport($data), 'vizitatori.xlsx');
	}

}
