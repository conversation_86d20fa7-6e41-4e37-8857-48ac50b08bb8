<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('visitors_extra_fields', function (Blueprint $table) {
            $table->id();
            $table->string('field_key')->unique(); // JSON key name
            $table->string('field_label'); // Display label
            $table->string('field_type')->default('text'); // text, email, phone, select, etc.
            $table->json('field_options')->nullable(); // For select fields
            $table->boolean('is_required')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('visitors_extra_fields');
    }
};
