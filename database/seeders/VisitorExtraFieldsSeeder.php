<?php

namespace Database\Seeders;

use App\Models\VisitorExtraField;
use Illuminate\Database\Seeder;

class VisitorExtraFieldsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fields = [
            [
                'field_key' => 'telefon',
                'field_label' => 'Telefon',
                'field_type' => 'phone',
                'is_required' => false,
                'sort_order' => 1,
            ],
            [
                'field_key' => 'companie',
                'field_label' => 'Companie',
                'field_type' => 'text',
                'is_required' => false,
                'sort_order' => 2,
            ],
            [
                'field_key' => 'functie',
                'field_label' => 'Funcție',
                'field_type' => 'text',
                'is_required' => false,
                'sort_order' => 3,
            ],
            [
                'field_key' => 'oras',
                'field_label' => 'Oraș',
                'field_type' => 'text',
                'is_required' => false,
                'sort_order' => 4,
            ],
            [
                'field_key' => 'judet',
                'field_label' => 'Județ',
                'field_type' => 'text',
                'is_required' => false,
                'sort_order' => 5,
            ],
            [
                'field_key' => 'adresa',
                'field_label' => 'Adresă',
                'field_type' => 'text',
                'is_required' => false,
                'sort_order' => 6,
            ],
        ];

        foreach ($fields as $field) {
            VisitorExtraField::updateOrCreate(
                ['field_key' => $field['field_key']],
                $field
            );
        }
    }
}
