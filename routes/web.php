<?php

use App\Http\Controllers\BadgeCategoryController;
use App\Http\Controllers\BadgeFieldController;
use App\Http\Controllers\ChangePasswordController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\InfoUserController;
use App\Http\Controllers\IstoricController;
use App\Http\Controllers\MagicLinkController;
use App\Http\Controllers\PdfTemplateController;
use App\Http\Controllers\RegisterController;
use App\Http\Controllers\ResetController;
use App\Http\Controllers\SessionsController;
use App\Http\Controllers\VisitorController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['middleware' => 'auth'], function () {
	Route::get('/login', [SessionsController::class, 'create']);
	Route::get('/', [HomeController::class, 'home']);
	Route::get('/dashboard', [HomeController::class, 'home'])->name('home');

	Route::get('/logout', [SessionsController::class, 'destroy']);
	Route::get('/login', function () {
		return view('dashboard');
	})->name('sign-up');
});

Route::group(['middleware' => ['auth', 'role:superadmin|admin']], function () {
	Route::group(['prefix' => 'expozanti'], function () {
		Route::get('/', function () {
			return view('expozanti');
		});
	});

	Route::group(['prefix' => 'vizitatori'], function () {
		Route::get('/', [VisitorController::class, 'index'])->name('visitors.index');
		Route::get('/create', [VisitorController::class, 'create'])->name('visitors.create');
		Route::post('/store', [VisitorController::class, 'store'])->name('visitors.store');
		Route::get('/edit/{id}', [VisitorController::class, 'edit'])->name('visitors.edit');
		Route::put('/update/{id}', [VisitorController::class, 'update'])->name('visitors.update');
		Route::delete('/delete/{id}', [VisitorController::class, 'destroy'])->name('visitors.destroy');
		Route::get('/badge/{id}', [VisitorController::class, 'badge'])->name('visitors.badge');
		Route::get('/blankBadge/{id}', [VisitorController::class, 'blankBadge'])->name('visitors.blankBadge');
		Route::get('/badges', [VisitorController::class, 'badges'])->name('visitors.badges');
		Route::post('/customBadge', [VisitorController::class, 'customBadge'])->name('visitors.customBadge');
		Route::get('/customBadgeView', [VisitorController::class, 'customBadgeView'])->name('visitors.customBadgeView');
		Route::get('/export', [VisitorController::class, 'export'])->name('visitors.export');

		Route::get('/identify-qr', [VisitorController::class, 'identifyQR'])->name('visitors.identifyQR');
		Route::post('/identify-qr', [VisitorController::class, 'identifyQRPost'])->name('visitors.identifyQRPost');

		Route::get('/badge-options', [VisitorController::class, 'badgeOptions'])->name('visitors.badgeOptions');
		Route::post('/badge-with-category/{id}', [VisitorController::class, 'generateBadgeWithCategory'])->name('visitors.badgeWithCategory');
		Route::post('/blank-badge-with-category/{id}', [VisitorController::class, 'generateBlankBadgeWithCategory'])->name('visitors.blankBadgeWithCategory');
	});

	// Badge Categories Management
	Route::resource('badge-categories', BadgeCategoryController::class);

	// PDF Templates Management
	Route::resource('pdf-templates', PdfTemplateController::class);
	Route::get('pdf-templates/{pdfTemplate}/test-badge', [PdfTemplateController::class, 'testBadge'])->name('pdf-templates.test-badge');

	// Badge Fields Management
	Route::get('pdf-templates/{template}/fields', [BadgeFieldController::class, 'index'])->name('badge-fields.index');
	Route::get('pdf-templates/{template}/fields/create', [BadgeFieldController::class, 'create'])->name('badge-fields.create');
	Route::post('pdf-templates/{template}/fields', [BadgeFieldController::class, 'store'])->name('badge-fields.store');
	Route::get('pdf-templates/{template}/fields/{badgeField}/edit', [BadgeFieldController::class, 'edit'])->name('badge-fields.edit');
	Route::put('pdf-templates/{template}/fields/{badgeField}', [BadgeFieldController::class, 'update'])->name('badge-fields.update');
	Route::delete('pdf-templates/{template}/fields/{badgeField}', [BadgeFieldController::class, 'destroy'])->name('badge-fields.destroy');
	Route::post('pdf-templates/{template}/fields/update-order', [BadgeFieldController::class, 'updateOrder'])->name('badge-fields.update-order');
	Route::post('pdf-templates/{template}/fields/update-all', [BadgeFieldController::class, 'updateAll'])->name('badge-fields.update-all');

	Route::get('delete-cache', function () {
		Artisan::call('cache:clear');
		Artisan::call('config:clear');
		Artisan::call('config:cache');
		Artisan::call('view:clear');
		return 'Cache is cleared';
	});
});

Route::group(['middleware' => ['auth', 'role:superadmin|admin|expozant']], function () {
	Route::post('/magic-links/{id}', [MagicLinkController::class, 'create'])->name('magic-links.create');
	Route::group(['prefix' => 'scan'], function () {
		Route::get('/', function () {
			return view('scan');
		});
		Route::post('search', [VisitorController::class, 'search'])->name('scan.search');
		Route::post('checkin', [VisitorController::class, 'checkin'])->name('scan.checkin');
	});

	Route::group(['prefix' => 'istoric'], function () {
		Route::get('/', [IstoricController::class, 'index'])->name('istoric.index');
		Route::get('/export', [IstoricController::class, 'export'])->name('istoric.export');
	});
});

Route::group(['middleware' => 'guest'], function () {
	Route::get('/login', [SessionsController::class, 'create']);
	Route::post('/session', [SessionsController::class, 'store']);
});
Route::get('/', function () {
	if (auth()->check()) {
		return redirect('dashboard');
	}
	return view('session/login-session');
})->name('login');

Route::get('/login/magic/{token}', [MagicLinkController::class, 'login'])->name('login.magic');