@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-gradient-primary">
                        <h4 class="text-white">Adaugă participant</h4>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <form action="{{ route('visitors.store') }}" method="POST">
                            @csrf
                            <div class="row info-personale">
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="nume" placeholder="Nume" value="{{ old('nume') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="prenume" placeholder="Prenume" value="{{ old('prenume') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="email" name="email" placeholder="Email" value="{{ old('email') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="telefon" placeholder="Telefon" value="{{ old('telefon') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="adresa_corespondenta" placeholder="Adresă" value="{{ old('adresa_corespondenta') }}">
                                </div>
                            </div>
                            <hr/>
                            <div class="participare-options my-3">
                                <div class="row">
                                    <div class="col-md-4">
                                        <select name="categorie" class="form-select select-tip-participare">
                                            <option value="">Selectează categoria</option>
                                            @foreach(\App\Models\BadgeCategory::all() as $category)
                                                <option value="{{ $category->name }}" {{ old('categorie') == $category->name ? 'selected' : '' }}>
                                                    {{ $category->display_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Adaugă</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).on('change', '.select-specializare', (e) => {
            // $('select[name="specializare"]').find('option').prop('selected', false);
            if ($(e.currentTarget).find('option:eq(1)').is(':selected')) {
                $(e.currentTarget).parents('form').find('.cuim, .grad_did, .membru_srs').removeClass('d-none').find('input, select').prop('disabled', false);
                $(e.currentTarget).parents('form').find('.alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
            } else if ($(e.currentTarget).find('option:eq(2)').is(':selected')) {
                $(e.currentTarget).parents('form').find('.cuim, .grad_did').removeClass('d-none').find('input, select').prop('disabled', false);
                $(e.currentTarget).parents('form').find('.membru_srs, .alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
            } else if ($(e.currentTarget).find('option:eq(7)').is(':selected')) {
                $(e.currentTarget).parents('form').find('.cuim, .grad_did').addClass('d-none').find('input, select').prop('disabled', true);
                $(e.currentTarget).parents('form').find('.membru_srs').addClass('d-none').find('input, select').prop('disabled', true);
                $(e.currentTarget).parents('form').find('.alta_spec').removeClass('d-none').find('input, select').prop('disabled', false);
            } else {
                $(e.currentTarget).parents('form').find('.cuim, .grad_did, .membru_srs, .alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
            }
        });
    </script>
@endsection