@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-gradient-primary">
                        <h4 class="text-white">Adaugă participant</h4>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <form action="{{ route('visitors.store') }}" method="POST">
                            @csrf
                            <div class="row info-personale">
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="nume" placeholder="Nume" value="{{ old('nume') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="prenume" placeholder="Prenume" value="{{ old('prenume') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="email" name="email" placeholder="Email" value="{{ old('email') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="telefon" placeholder="Telefon" value="{{ old('telefon') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="adresa_corespondenta" placeholder="Adresă" value="{{ old('adresa_corespondenta') }}">
                                </div>
                                <div class="col-md-4 mt-3">
                                    <input class="form-control" type="text" name="regiune" placeholder="Regiune" value="{{ old('regiune') }}">
                                </div>

                                <!-- Dynamic Extra Fields Container -->
                                <div id="extra-fields-container" class="w-100">
                                    <!-- Extra fields will be added here dynamically -->
                                </div>

                                <!-- Add Extra Field Button -->
                                <div class="col-md-4 mt-3">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="add-extra-field">
                                        <i class="fas fa-plus"></i> Adaugă câmp suplimentar
                                    </button>
                                </div>
                            </div>
                            <hr/>
                            <div class="participare-options my-3">
                                <div class="row">
                                    <div class="col-md-4">
                                        <select name="categorie" class="form-select select-tip-participare">
                                            <option value="">Selectează categoria</option>
                                            @foreach(\App\Models\BadgeCategory::all() as $category)
                                                <option value="{{ $category->name }}" {{ old('categorie') == $category->name ? 'selected' : '' }}>
                                                    {{ $category->display_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Adaugă</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        // Available extra fields from database
        const availableFields = @json(\App\Models\VisitorExtraField::getActiveFields());
        let addedFields = [];

        document.addEventListener('DOMContentLoaded', function() {
            const addButton = document.getElementById('add-extra-field');
            const container = document.getElementById('extra-fields-container');

            addButton.addEventListener('click', function() {
                showFieldSelector();
            });

            function showFieldSelector() {
                const unusedFields = availableFields.filter(field => !addedFields.includes(field.field_key));

                if (unusedFields.length === 0) {
                    alert('Toate câmpurile disponibile au fost adăugate.');
                    return;
                }

                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Selectați câmpul de adăugat</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <select class="form-control" id="field-selector">
                                    <option value="">Alegeți un câmp...</option>
                                    ${unusedFields.map(field => `<option value="${field.field_key}">${field.field_label}</option>`).join('')}
                                </select>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                                <button type="button" class="btn btn-primary" onclick="addSelectedField()">Adaugă</button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();

                modal.addEventListener('hidden.bs.modal', function() {
                    document.body.removeChild(modal);
                });
            }

            window.addSelectedField = function() {
                const selector = document.getElementById('field-selector');
                const fieldKey = selector.value;

                if (!fieldKey) {
                    alert('Vă rog să selectați un câmp.');
                    return;
                }

                const field = availableFields.find(f => f.field_key === fieldKey);
                addExtraField(field);
                addedFields.push(fieldKey);

                // Close modal
                const modal = selector.closest('.modal');
                bootstrap.Modal.getInstance(modal).hide();
            };

            function addExtraField(field) {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'col-md-4 mt-3';
                fieldDiv.setAttribute('data-field-key', field.field_key);

                let inputHtml = '';
                if (field.field_type === 'select') {
                    const options = field.field_options || [];
                    inputHtml = `
                        <select class="form-control" name="extra_${field.field_key}" ${field.is_required ? 'required' : ''}>
                            <option value="">${field.field_label}</option>
                            ${options.map(option => `<option value="${option}">${option}</option>`).join('')}
                        </select>
                    `;
                } else {
                    const inputType = field.field_type === 'phone' ? 'tel' : field.field_type;
                    inputHtml = `
                        <input class="form-control"
                               type="${inputType}"
                               name="extra_${field.field_key}"
                               placeholder="${field.field_label}"
                               ${field.is_required ? 'required' : ''}>
                    `;
                }

                fieldDiv.innerHTML = `
                    <div class="input-group">
                        ${inputHtml}
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeExtraField('${field.field_key}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                container.appendChild(fieldDiv);
            }

            window.removeExtraField = function(fieldKey) {
                const fieldDiv = document.querySelector(`[data-field-key="${fieldKey}"]`);
                if (fieldDiv) {
                    fieldDiv.remove();
                    addedFields = addedFields.filter(key => key !== fieldKey);
                }
            };
        });

        $(document).on('change', '.select-specializare', (e) => {
            // $('select[name="specializare"]').find('option').prop('selected', false);
            if ($(e.currentTarget).find('option:eq(1)').is(':selected')) {
                $(e.currentTarget).parents('form').find('.cuim, .grad_did, .membru_srs').removeClass('d-none').find('input, select').prop('disabled', false);
                $(e.currentTarget).parents('form').find('.alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
            } else if ($(e.currentTarget).find('option:eq(2)').is(':selected')) {
                $(e.currentTarget).parents('form').find('.cuim, .grad_did').removeClass('d-none').find('input, select').prop('disabled', false);
                $(e.currentTarget).parents('form').find('.membru_srs, .alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
            } else if ($(e.currentTarget).find('option:eq(7)').is(':selected')) {
                $(e.currentTarget).parents('form').find('.cuim, .grad_did').addClass('d-none').find('input, select').prop('disabled', true);
                $(e.currentTarget).parents('form').find('.membru_srs').addClass('d-none').find('input, select').prop('disabled', true);
                $(e.currentTarget).parents('form').find('.alta_spec').removeClass('d-none').find('input, select').prop('disabled', false);
            } else {
                $(e.currentTarget).parents('form').find('.cuim, .grad_did, .membru_srs, .alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
            }
        });
    </script>
@endsection