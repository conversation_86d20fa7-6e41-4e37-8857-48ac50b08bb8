@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header bg-gradient-primary">
                <h4 class="text-white">Opțiuni generare badge-uri</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Bulk Badge Generation -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-gradient-info">
                                <h5 class="text-white mb-0">
                                    <i class="fas fa-layer-group me-2"></i>
                                    Generare în masă
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Generează badge-uri pentru toți participanții dintr-o categorie.</p>
                                @foreach($categories as $category)
                                    <div class="d-grid gap-2 mb-2">
                                        <a href="{{ route('visitors.badges', ['tip' => $category->name]) }}" target="_blank" class="btn btn-outline-primary">
                                            <i class="fas fa-users me-2"></i>
                                            Badge-uri {{ $category->display_name }}
                                            <span class="badge bg-secondary ms-2">
                                                {{ $category->visitors()->count() }} participanți
                                            </span>
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    <!-- Individual Badge Generation -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-gradient-success">
                                <h5 class="text-white mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    Generare individuală
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Generează badge pentru un participant specific cu categoria dorită.</p>
                                <form id="individualBadgeForm">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="visitor_select" class="form-label">Selectează participantul</label>
                                        <select class="form-select" id="visitor_select" required>
                                            <option value="">Alege participantul...</option>
                                            @foreach(\App\Models\Vizitator::orderBy('nume')->get() as $visitor)
                                                <option value="{{ $visitor->id }}">
                                                    {{ $visitor->nume }} {{ $visitor->prenume }}
                                                    ({{ $visitor->categorie }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="category_select" class="form-label">Selectează categoria badge-ului</label>
                                        <select class="form-select" id="category_select" required>
                                            <option value="">Alege categoria...</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->name }}">
                                                    {{ $category->display_name }}
                                                    @if($category->pdfTemplate)
                                                        <span class="text-success">(Template configurat)</span>
                                                    @else
                                                        <span class="text-warning">(Template lipsă)</span>
                                                    @endif
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary" onclick="generateBadge(false)">
                                            <i class="fas fa-id-card me-2"></i>
                                            Generează Badge Normal
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="generateBadge(true)">
                                            <i class="fas fa-file-alt me-2"></i>
                                            Generează Badge Gol (fără template)
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Template Testing -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-gradient-warning">
                                <h5 class="text-white mb-0">
                                    <i class="fas fa-vial me-2"></i>
                                    Testare template-uri
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Testează pozițiile câmpurilor pentru fiecare template PDF.</p>
                                <div class="row">
                                    @foreach($categories as $category)
                                        @if($category->pdfTemplate)
                                            <div class="col-md-3 mb-2">
                                                <a href="{{ route('pdf-templates.test-badge', $category->pdfTemplate) }}" target="_blank" class="btn btn-outline-warning w-100">
                                                    <i class="fas fa-flask me-2"></i>
                                                    Test {{ $category->display_name }}
                                                </a>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                                @if($categories->where('pdfTemplate', null)->count() > 0)
                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Categorii fără template:</strong>
                                        @foreach($categories->where('pdfTemplate', null) as $category)
                                            <span class="badge bg-warning text-dark me-1">{{ $category->display_name }}</span>
                                        @endforeach
                                        <br>
                                        <small>Aceste categorii vor folosi template-urile hardcodate din cod.</small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ route('visitors.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Înapoi la participanți
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function generateBadge(isBlank) {
            const visitorId = document.getElementById('visitor_select').value;
            const category  = document.getElementById('category_select').value;

            if (!visitorId || !category) {
                alert('Te rog să selectezi atât participantul cât și categoria.');
                return;
            }

            const form  = document.createElement('form');
            form.method = 'POST';
            form.target = '_blank';

            if (isBlank) {
                form.action = `{{ url('vizitatori/blank-badge-with-category') }}/${visitorId}`;
            } else {
                form.action = `{{ url('vizitatori/badge-with-category') }}/${visitorId}`;
            }

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type  = 'hidden';
            csrfToken.name  = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add category
            const categoryInput = document.createElement('input');
            categoryInput.type  = 'hidden';
            categoryInput.name  = 'category';
            categoryInput.value = category;
            form.appendChild(categoryInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }
    </script>
@endsection
