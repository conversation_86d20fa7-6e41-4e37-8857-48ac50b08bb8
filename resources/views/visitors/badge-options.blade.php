@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header bg-gradient-primary">
                <h4 class="text-white">Opțiuni generare badge-uri</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Bulk Badge Generation -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-gradient-info">
                                <h5 class="text-white mb-0">
                                    <i class="fas fa-layer-group me-2"></i>
                                    Generare în masă
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Generează badge-uri pentru toți participanții dintr-o categorie.</p>
                                @foreach($categories as $category)
                                    <div class="d-grid gap-2 mb-2">
                                        <a href="{{ route('visitors.badges', ['tip' => $category->name]) }}" target="_blank" class="btn btn-outline-primary">
                                            <i class="fas fa-users me-2"></i>
                                            Badge-uri {{ $category->display_name }}
                                            <span class="badge bg-secondary ms-2">
                                                {{ $category->visitors()->count() }} participanți
                                            </span>
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    <!-- Individual Badge Generation -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-gradient-success">
                                <h5 class="text-white mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    Generare individuală
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Generează badge pentru un participant specific cu categoria dorită.</p>
                                <form id="individualBadgeForm">
                                    @csrf
                                    <div class="mb-3">
                                        <label for="visitor_search" class="form-label">Căutați participantul</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="visitor_search" placeholder="Căutați după nume, prenume sau email..." autocomplete="off">
                                            <input type="hidden" id="visitor_select" required>
                                            <div id="visitor_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto; display: none; position: absolute; z-index: 1000;">
                                                <!-- Search results will be populated here -->
                                            </div>
                                        </div>
                                        <small class="text-muted">Participant selectat: <span id="selected_visitor_name">Niciunul</span></small>
                                    </div>
                                    <div class="mb-3">
                                        <label for="category_select" class="form-label">Selectează categoria badge-ului</label>
                                        <select class="form-select" id="category_select" required>
                                            <option value="">Alege categoria...</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->name }}">
                                                    {{ $category->display_name }}
                                                    @if($category->pdfTemplate)
                                                        <span class="text-success">(Template configurat)</span>
                                                    @else
                                                        <span class="text-warning">(Template lipsă)</span>
                                                    @endif
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary" onclick="generateBadge(false)">
                                            <i class="fas fa-id-card me-2"></i>
                                            Generează Badge Normal
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" onclick="generateBadge(true)">
                                            <i class="fas fa-file-alt me-2"></i>
                                            Generează Badge Gol (fără template)
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Template Testing -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-gradient-warning">
                                <h5 class="text-white mb-0">
                                    <i class="fas fa-vial me-2"></i>
                                    Testare template-uri
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Testează pozițiile câmpurilor pentru fiecare template PDF.</p>
                                <div class="row">
                                    @foreach($categories as $category)
                                        @if($category->pdfTemplate)
                                            <div class="col-md-3 mb-2">
                                                <a href="{{ route('pdf-templates.test-badge', $category->pdfTemplate) }}" target="_blank" class="btn btn-outline-warning w-100">
                                                    <i class="fas fa-flask me-2"></i>
                                                    Test {{ $category->display_name }}
                                                </a>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                                @if($categories->where('pdfTemplate', null)->count() > 0)
                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Categorii fără template:</strong>
                                        @foreach($categories->where('pdfTemplate', null) as $category)
                                            <span class="badge bg-warning text-dark me-1">{{ $category->display_name }}</span>
                                        @endforeach
                                        <br>
                                        <small>Aceste categorii vor folosi template-urile hardcodate din cod.</small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ route('visitors.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Înapoi la participanți
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        // Visitor search functionality
        const visitors = {{ Illuminate\Support\Js::from(\App\Models\Vizitator::select('id', 'nume', 'prenume', 'email', 'categorie')->orderBy('nume')->get()) }};

        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('visitor_search');
            const dropdown = document.getElementById('visitor_dropdown');
            const hiddenSelect = document.getElementById('visitor_select');
            const selectedName = document.getElementById('selected_visitor_name');

            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase().trim();

                if (query.length < 2) {
                    dropdown.style.display = 'none';
                    return;
                }

                const filteredVisitors = visitors.filter(visitor =>
                    visitor.nume.toLowerCase().includes(query) ||
                    visitor.prenume.toLowerCase().includes(query) ||
                    visitor.email.toLowerCase().includes(query) ||
                    (visitor.nume + ' ' + visitor.prenume).toLowerCase().includes(query)
                );

                if (filteredVisitors.length > 0) {
                    dropdown.innerHTML = filteredVisitors.map(visitor =>
                        `<a class="dropdown-item" href="#" data-visitor-id="${visitor.id}" data-visitor-name="${visitor.nume} ${visitor.prenume}">
                            <strong>${visitor.nume} ${visitor.prenume}</strong><br>
                            <small class="text-muted">${visitor.email} (${visitor.categorie})</small>
                        </a>`
                    ).join('');
                    dropdown.style.display = 'block';
                } else {
                    dropdown.innerHTML = '<div class="dropdown-item-text text-muted">Nu s-au găsit rezultate</div>';
                    dropdown.style.display = 'block';
                }
            });

            // Handle visitor selection
            dropdown.addEventListener('click', function(e) {
                e.preventDefault();
                const item = e.target.closest('[data-visitor-id]');
                if (item) {
                    const visitorId = item.getAttribute('data-visitor-id');
                    const visitorName = item.getAttribute('data-visitor-name');

                    hiddenSelect.value = visitorId;
                    searchInput.value = visitorName;
                    selectedName.textContent = visitorName;
                    dropdown.style.display = 'none';
                }
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.position-relative')) {
                    dropdown.style.display = 'none';
                }
            });

            // Clear selection when input is cleared
            searchInput.addEventListener('keyup', function() {
                if (this.value === '') {
                    hiddenSelect.value = '';
                    selectedName.textContent = 'Niciunul';
                }
            });
        });

        function generateBadge(isBlank) {
            const visitorId = document.getElementById('visitor_select').value;
            const category  = document.getElementById('category_select').value;

            if (!visitorId || !category) {
                alert('Te rog să selectezi atât participantul cât și categoria.');
                return;
            }

            const form  = document.createElement('form');
            form.method = 'POST';
            form.target = '_blank';

            if (isBlank) {
                form.action = `{{ url('vizitatori/blank-badge-with-category') }}/${visitorId}`;
            } else {
                form.action = `{{ url('vizitatori/badge-with-category') }}/${visitorId}`;
            }

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type  = 'hidden';
            csrfToken.name  = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add category
            const categoryInput = document.createElement('input');
            categoryInput.type  = 'hidden';
            categoryInput.name  = 'category';
            categoryInput.value = category;
            form.appendChild(categoryInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }
    </script>
@endsection
