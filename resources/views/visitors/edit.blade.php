@extends('layouts.user_type.auth')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient-primary">
                    <h4 class="text-white">Actualizați participant</h4>
                </div>
                <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                    <form action="{{ route('visitors.update', $visitor->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row info-personale">
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="nume" placeholder="Nume" value="{{ old('nume', $visitor->nume) }}">
                                @error('nume') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="prenume" placeholder="Prenume" value="{{ old('prenume', $visitor->prenume) }}">
                                @error('prenume') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="email" name="email" placeholder="Email" value="{{ old('email', $visitor->email) }}">
                                @error('email') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="telefon" placeholder="Telefon" value="{{ old('telefon', $visitor->date->telefon ?? '') }}">
                                @error('telefon') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="adresa_corespondenta" placeholder="Adresă" value="{{ old('adresa_corespondenta', $visitor->date->adresa_corespondenta ?? '') }}">
                                @error('adresa') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="regiune" placeholder="Oraș/țară" value="{{ old('regiune', $visitor->regiune ?? '') }}">
                                @error('adresa') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                        </div>
                        <hr/>
                        <div class="participare-options my-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <select name="categorie" class="form-select select-tip-participare">
                                        <option value="">Selectează categoria</option>
                                        @foreach(\App\Models\BadgeCategory::all() as $category)
                                            <option value="{{ $category->name }}" {{ old('categorie', $visitor->categorie) == $category->name ? 'selected' : '' }}>
                                                {{ $category->display_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('categorie') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Actualizați</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="card mt-4">
                <div class="card-header bg-gradient-warning">
                    <h4 class="text-white">Generați un badge customizat</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('visitors.customBadge') }}" method="POST" target="_blank" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="id_vizitator" value="{{ $visitor->id }}">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="mb-3">
                                    <label for="nume" class="form-label">Nume</label>
                                    <input type="text" name="nume" class="form-control" id="nume" value="{{ old('nume', $visitor->nume) }}">
                                    @error('nume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="mb-3">
                                    <label for="prenume" class="form-label">Prenume</label>
                                    <input type="text" name="prenume" class="form-control" id="prenume" value="{{ old('prenume', $visitor->prenume) }}">
                                    @error('prenume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_nume" class="form-label">Font size nume</label>
                                    <input type="number" name="font_size_nume" class="form-control" id="font_size_nume" value="{{ old('font_size_nume', 20) }}">
                                    @error('font_size_nume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="logo" class="form-label">Logo</label>
                                    <input type="file" name="logo" class="form-control" id="logo">
                                    @error('logo') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-10">
                                <div class="mb-3">
                                    <label for="tip" class="form-label">Label</label>
                                    <select name="label" class="form-select" id="tip">
                                        <option value="PARTICIPANT" {{ old('label', $visitor->categorie == 'participant' ? "PARTICIPANT" : '') === 'PARTICIPANT' ? 'selected' : '' }}>PARTICIPANT</option>
                                        <option value="SPEAKER" {{ old('label', $visitor->categorie == 'speaker' ? "SPEAKER" : '') === 'SPEAKER' ? 'selected' : '' }}>SPEAKER</option>
                                        <option value="STAFF" {{ old('label', $visitor->categorie == 'staff' ? "STAFF" : '') === 'STAFF' ? 'selected' : '' }}>STAFF</option>
                                    </select>
                                    @error('tip')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_label" class="form-label">Font size label</label>
                                    <input type="number" name="font_size_label" class="form-control" id="font_size_label" value="{{ old('font_size_label', 16) }}">
                                    @error('font_size_label')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-10">
                                <div class="mb-3">
                                    <label for="regiune" class="form-label">Regiune</label>
                                    <input type="text" name="regiune" class="form-control" id="regiune" value="{{ old('regiune', $visitor->regiune) }}">
                                    @error('regiune')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_regiune" class="form-label">Font size regiune</label>
                                    <input type="number" name="font_size_regiune" class="form-control" id="font_size_regiune" value="{{ old('font_size_regiune', 12) }}">
                                    @error('font_size_regiune')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-warning">Generați</button>
                    </form>
                    <div class="mt-3">
                        <img src="{{ $img }}" alt="QR Code" class="w-50"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).on('change', '.select-specializare', (e) => {
        if ($(e.currentTarget).find('option:eq(1)').is(':selected')) {
            $(e.currentTarget).parents('form').find('.cuim, .grad_did, .membru_srs').removeClass('d-none').find('input, select').prop('disabled', false);
            $(e.currentTarget).parents('form').find('.alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
        } else if ($(e.currentTarget).find('option:eq(2)').is(':selected')) {
            $(e.currentTarget).parents('form').find('.cuim, .grad_did').removeClass('d-none').find('input, select').prop('disabled', false);
            $(e.currentTarget).parents('form').find('.membru_srs, .alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
        } else if ($(e.currentTarget).find('option:eq(7)').is(':selected')) {
            $(e.currentTarget).parents('form').find('.cuim, .grad_did').addClass('d-none').find('input, select').prop('disabled', true);
            $(e.currentTarget).parents('form').find('.membru_srs').addClass('d-none').find('input, select').prop('disabled', true);
            $(e.currentTarget).parents('form').find('.alta_spec').removeClass('d-none').find('input, select').prop('disabled', false);
        } else {
            $(e.currentTarget).parents('form').find('.cuim, .grad_did, .membru_srs, .alta_spec').addClass('d-none').find('input, select').prop('disabled', true);
        }
    });
</script>
@endsection