@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header bg-gradient-primary">
                        <div>
                            <h4 class="text-white">Participanți înregistrați</h4>
                            <a href="{{ route('visitors.badgeOptions') }}" class="btn btn-success me-2">
                                <i class="fas fa-id-card me-1"></i>Opțiuni Badge-uri
                            </a>
{{--                            @foreach(\App\Models\BadgeCategory::all() as $category)--}}
{{--                                <a href="{{ route('visitors.badges', ['tip' => $category->name]) }}" target="_blank" class="btn btn-info me-1">--}}
{{--                                    {{ $category->display_name }}--}}
{{--                                </a>--}}
{{--                            @endforeach--}}
                            <a href="{{ route('visitors.customBadgeView') }}" class="btn btn-warning text-white bg-gradient-warning">Badge personalizat</a>
                            <a href="{{ route('visitors.create') }}" class="btn btn-light float-end">Adaugă participant</a>
                        </div>
                        <div>
                            <a href="{{ route('visitors.identifyQR') }}" class="btn btn-light">Identifică QR</a>
                            <a href="{{ route('visitors.export') }}" class="btn btn-light bg-gradient-success float-end">Exportă participanți</a>
                        </div>
                        <div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if ($visitors->count())
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="visitors">
                                    <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Nume</th>
                                        <th>Email</th>
                                        <th>Acțiuni</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach ($visitors as $visitor)
                                        <tr>
                                            <td>{{ $loop->iteration }}</td>
                                            <td>{{ $visitor->nume . ' ' . $visitor->prenume }}</td>
                                            <td>{{ $visitor->email }}</td>
                                            <td>
                                                <div class="text-end">
                                                    <a href="{{ route('visitors.blankBadge', $visitor->id) }}" target="_blank" class="btn btn-info btn-sm my-1">Blank</a>
                                                    <a href="{{ route('visitors.badge', $visitor->id) }}" target="_blank" class="btn btn-info btn-sm my-1">Badge</a>
                                                </div>
                                                <div class="text-end">
                                                    <a href="{{ route('visitors.edit', $visitor->id) }}" class="btn btn-warning btn-sm my-1">Editare</a>
                                                    <form action="{{ route('visitors.destroy', $visitor->id) }}" method="POST" style="display:inline-block;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-sm my-1" onclick="return confirm('Are you sure you want to delete this participant?');">Șterge</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-center">Nu au fost găsiți participanți înregistrați.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('scripts')
    <script>
        $(document).ready(function () {
            $('#visitors').DataTable({
                "order": [[0, "desc"]]
            });
        });
    </script>
@endsection