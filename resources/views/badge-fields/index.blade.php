@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header bg-gradient-primary">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="text-white">Gestionează Câmpuri - {{ $template->category->display_name }}</h4>
                                <small class="text-white opacity-8">{{ $template->original_name }}</small>
                            </div>
                            <div>
                                <a href="{{ route('pdf-templates.test-badge', $template) }}"
                                   class="btn btn-success me-2"
                                   target="_blank">
                                    <i class="fas fa-vial me-1"></i>Test Badge
                                </a>
                                <button type="button" class="btn btn-light" onclick="addNewField()">
                                    <i class="fas fa-plus me-1"></i><PERSON><PERSON><PERSON> câmp
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        <div class="alert alert-info text-white">
                            <i class="fas fa-info-circle me-1"></i>
                            Folosește butonul "Test Badge" pentru a verifica pozițiile câmpurilor.
                            Câmpurile pot fi reordonate prin drag & drop.
                        </div>

                        <!-- Fields Management Interface -->
                            {{-- header with column names --}}
                        <div class="row mb-3">
                            <div class="col-md-1">
                            </div>
                            <div class="col-md-2">
                                <strong>Tip Câmp</strong>
                            </div>
                            <div class="col-md-2">
                                <strong>Cheie JSON</strong>
                            </div>
                            <div class="col-md-1">
                                <strong>X</strong>
                            </div>
                            <div class="col-md-1">
                                <strong>Y</strong>
                            </div>
                            <div class="col-md-1">
                                <strong>Font</strong>
                            </div>
                            <div class="col-md-1">
                                <strong>Lățime</strong>
                            </div>
                            <div class="col-md-1">
                                <strong>Aliniere</strong>
                            </div>
                            <div class="col-md-1">
                                <strong>Activ</strong>
                            </div>
                            <div class="col-md-1">
                                <strong>Acțiuni</strong>
                            </div>
                        </div>
                        <div id="fieldsContainer">
                            @foreach($template->badgeFields()->orderBy('sort_order')->get() as $field)
                                <div class="field-item card mb-3" data-field-id="{{ $field->id }}">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-1">
                                                <i class="fas fa-grip-vertical text-muted cursor-move"></i>
                                            </div>
                                            <div class="col-md-2">
                                                <select class="form-select field-type" data-field-id="{{ $field->id }}">
                                                    @foreach(\App\Models\BadgeField::getBuiltInFields() as $key => $label)
                                                        <option value="{{ $key }}" {{ $field->field_name == $key ? 'selected' : '' }}>
                                                            {{ $label }}
                                                        </option>
                                                    @endforeach
                                                    @if(!in_array($field->field_name, array_keys(\App\Models\BadgeField::getBuiltInFields())))
                                                        <option value="{{ $field->field_name }}" selected>{{ $field->field_name }}</option>
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <input type="text" class="form-control json-key"
                                                       placeholder="Cheie JSON"
                                                       value="{{ $field->json_key }}"
                                                       data-field-id="{{ $field->id }}"
                                                       {{ in_array($field->field_name, array_keys(\App\Models\BadgeField::getBuiltInFields())) ? 'disabled' : '' }}>
                                            </div>
                                            <div class="col-md-1">
                                                <input type="number" class="form-control x-coordinate"
                                                       placeholder="X"
                                                       value="{{ $field->x_coordinate }}"
                                                       data-field-id="{{ $field->id }}"
                                                       min="0" max="1000">
                                            </div>
                                            <div class="col-md-1">
                                                <input type="number" class="form-control y-coordinate"
                                                       placeholder="Y"
                                                       value="{{ $field->y_coordinate }}"
                                                       data-field-id="{{ $field->id }}"
                                                       min="0" max="1000">
                                            </div>
                                            <div class="col-md-1">
                                                <input type="number" class="form-control font-size"
                                                       placeholder="Font"
                                                       value="{{ $field->font_size }}"
                                                       data-field-id="{{ $field->id }}"
                                                       min="6" max="72"
                                                       title="Dimensiune font (pentru QR = dimensiune QR)">
                                            </div>
                                            <div class="col-md-1">
                                                <input type="number" class="form-control max-width"
                                                       placeholder="Lățime"
                                                       value="{{ $field->max_width }}"
                                                       data-field-id="{{ $field->id }}"
                                                       min="0" max="1000">
                                            </div>
                                            <div class="col-md-1">
                                                <select class="form-select alignment" data-field-id="{{ $field->id }}">
                                                    <option value="L" {{ $field->alignment == 'L' ? 'selected' : '' }}>L</option>
                                                    <option value="C" {{ $field->alignment == 'C' ? 'selected' : '' }}>C</option>
                                                    <option value="R" {{ $field->alignment == 'R' ? 'selected' : '' }}>R</option>
                                                </select>
                                            </div>
                                            <div class="col-md-1">
                                                <div class="form-check">
                                                    <input class="form-check-input is-active"
                                                           type="checkbox"
                                                           {{ $field->is_active ? 'checked' : '' }}
                                                           data-field-id="{{ $field->id }}">
                                                </div>
                                            </div>
                                            <div class="col-md-1">
                                                <button type="button" class="btn btn-outline-danger m-0" onclick="deleteField({{ $field->id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Add New Field Template (Hidden) -->
                        <div id="newFieldTemplate" style="display: none;">
                            <div class="field-item card mb-3" data-field-id="new">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-1">
                                            <i class="fas fa-grip-vertical text-muted cursor-move"></i>
                                        </div>
                                        <div class="col-md-2">
                                            <select class="form-select field-type" data-field-id="new">
                                                <option value="">Selectează tipul</option>
                                                @foreach(\App\Models\BadgeField::getBuiltInFields() as $key => $label)
                                                    <option value="{{ $key }}">{{ $label }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="text" class="form-control json-key"
                                                   placeholder="Cheie JSON"
                                                   data-field-id="new">
                                        </div>
                                        <div class="col-md-1">
                                            <input type="number" class="form-control x-coordinate"
                                                   placeholder="X" value="0"
                                                   data-field-id="new"
                                                   min="0" max="1000">
                                        </div>
                                        <div class="col-md-1">
                                            <input type="number" class="form-control y-coordinate"
                                                   placeholder="Y" value="50"
                                                   data-field-id="new"
                                                   min="0" max="1000">
                                        </div>
                                        <div class="col-md-1">
                                            <input type="number" class="form-control font-size"
                                                   placeholder="Font" value="12"
                                                   data-field-id="new"
                                                   min="6" max="72">
                                        </div>
                                        <div class="col-md-1">
                                            <input type="number" class="form-control max-width"
                                                   placeholder="Lățime" value="0"
                                                   data-field-id="new"
                                                   min="0" max="1000">
                                        </div>
                                        <div class="col-md-1">
                                            <select class="form-select alignment" data-field-id="new">
                                                <option value="L">L</option>
                                                <option value="C" selected>C</option>
                                                <option value="R">R</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1">
                                            <div class="form-check">
                                                <input class="form-check-input is-active"
                                                       type="checkbox"
                                                       checked
                                                       data-field-id="new">
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-outline-success" onclick="saveNewField()">
                                                <i class="fas fa-save"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="cancelNewField()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 d-flex justify-content-between">
                            <a href="{{ route('pdf-templates.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Înapoi la Template-uri
                            </a>
                            <button type="button" class="btn btn-primary" onclick="saveAllFields()">
                                <i class="fas fa-save me-1"></i>Salvează toate modificările
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fieldsContainer = document.getElementById('fieldsContainer');
    if (fieldsContainer) {
        const sortable = Sortable.create(fieldsContainer, {
            handle: '.cursor-move',
            animation: 150,
            onEnd: function(evt) {
                // Update order after drag & drop
                updateFieldOrder();
            }
        });
    }

    // Handle field type changes
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('field-type')) {
            const fieldId = e.target.getAttribute('data-field-id');
            const jsonKeyInput = document.querySelector(`input.json-key[data-field-id="${fieldId}"]`);
            const builtInFields = @json(array_keys(\App\Models\BadgeField::getBuiltInFields()));

            if (builtInFields.includes(e.target.value)) {
                jsonKeyInput.disabled = true;
                jsonKeyInput.value = '';
            } else {
                jsonKeyInput.disabled = false;
            }
        }
    });
});

function addNewField() {
    const template = document.getElementById('newFieldTemplate');
    const clone = template.cloneNode(true);
    clone.id = '';
    clone.style.display = 'block';
    document.getElementById('fieldsContainer').appendChild(clone);
}

function saveNewField() {
    const newFieldElement = document.querySelector('[data-field-id="new"]');
    const fieldType = newFieldElement.querySelector('.field-type').value;
    const jsonKey = newFieldElement.querySelector('.json-key').value;
    const xCoordinate = newFieldElement.querySelector('.x-coordinate').value;
    const yCoordinate = newFieldElement.querySelector('.y-coordinate').value;
    const fontSize = newFieldElement.querySelector('.font-size').value;
    const maxWidth = newFieldElement.querySelector('.max-width').value;
    const alignment = newFieldElement.querySelector('.alignment').value;
    const isActive = newFieldElement.querySelector('.is-active').checked;

    if (!fieldType) {
        alert('Te rog să selectezi tipul câmpului.');
        return;
    }

    const builtInFields = @json(array_keys(\App\Models\BadgeField::getBuiltInFields()));
    if (!builtInFields.includes(fieldType) && !jsonKey) {
        alert('Te rog să introduci cheia JSON pentru câmpul personalizat.');
        return;
    }

    fetch('{{ route("badge-fields.store", $template) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            field_name: fieldType,
            json_key: jsonKey,
            x_coordinate: xCoordinate,
            y_coordinate: yCoordinate,
            font_size: fontSize,
            max_width: maxWidth,
            alignment: alignment,
            is_active: isActive
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => Promise.reject(err));
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            let errorMessage = data.message || 'Eroare necunoscută';
            if (data.errors) {
                const errorList = Object.values(data.errors).flat().join('\n');
                errorMessage += '\n\nDetalii:\n' + errorList;
            }
            alert('Eroare la salvarea câmpului: ' + errorMessage);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        let errorMessage = 'Eroare la salvarea câmpului.';
        if (error.message) {
            errorMessage += '\n' + error.message;
        }
        if (error.errors) {
            const errorList = Object.values(error.errors).flat().join('\n');
            errorMessage += '\n\nDetalii:\n' + errorList;
        }
        alert(errorMessage);
    });
}

function cancelNewField() {
    const newFieldElement = document.querySelector('[data-field-id="new"]');
    if (newFieldElement) {
        newFieldElement.remove();
    }
}

function deleteField(fieldId) {
    if (!confirm('Sigur doriți să ștergeți acest câmp?')) {
        return;
    }

    fetch(`{{ route("badge-fields.destroy", [$template, "FIELD_ID"]) }}`.replace('FIELD_ID', fieldId), {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelector(`[data-field-id="${fieldId}"]`).remove();
        } else {
            alert('Eroare la ștergerea câmpului.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Eroare la ștergerea câmpului.');
    });
}

function saveAllFields() {
    const fields = [];
    document.querySelectorAll('.field-item[data-field-id]:not([data-field-id="new"])').forEach((element, index) => {
        const fieldId = element.getAttribute('data-field-id');
        fields.push({
            id: fieldId,
            field_name: element.querySelector('.field-type').value,
            json_key: element.querySelector('.json-key').value,
            x_coordinate: element.querySelector('.x-coordinate').value,
            y_coordinate: element.querySelector('.y-coordinate').value,
            font_size: element.querySelector('.font-size').value,
            max_width: element.querySelector('.max-width').value,
            alignment: element.querySelector('.alignment').value,
            is_active: element.querySelector('.is-active').checked,
            sort_order: index + 1
        });
    });

    fetch('{{ route("badge-fields.update-all", $template) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            fields: fields
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Toate modificările au fost salvate cu succes!');
        } else {
            alert('Eroare la salvarea modificărilor: ' + (data.message || 'Eroare necunoscută'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Eroare la salvarea modificărilor.');
    });
}

function updateFieldOrder() {
    const fieldIds = Array.from(document.querySelectorAll('.field-item[data-field-id]:not([data-field-id="new"])')).map(element =>
        element.getAttribute('data-field-id')
    );

    fetch('{{ route("badge-fields.update-order", $template) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            field_ids: fieldIds
        })
    })
    .then(response => response.json())
    .catch(error => console.error('Error:', error));
}
</script>
<style>
.cursor-move {
    cursor: move;
}
.field-item {
    transition: all 0.3s ease;
}
.field-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
@endsection
