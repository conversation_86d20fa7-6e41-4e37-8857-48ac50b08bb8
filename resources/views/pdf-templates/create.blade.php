@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header bg-gradient-primary">
                <h4 class="text-white">Adaugă template PDF</h4>
            </div>
            <div class="card-body">
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif
                <form action="{{ route('pdf-templates.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="category_id" class="form-label">Categorie badge
                            <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                            <option value="">Selectează categoria</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}"
                                        {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->display_name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-3">
                        <label for="pdf_file" class="form-label">Fișier PDF
                            <span class="text-danger">*</span>
                        </label>
                        <input type="file" class="form-control @error('pdf_file') is-invalid @enderror" id="pdf_file" name="pdf_file" accept=".pdf" required>
                        <div class="form-text">
                            Încarcă un fișier PDF cu o singură pagină (max 10MB)
                        </div>
                        @error('pdf_file')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Notă:</strong> După crearea template-ului, vei putea configura câmpurile de text și poziția codului QR
                        în secțiunea "Gestionează câmpuri".
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('pdf-templates.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Înapoi
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>
                            Încarcă template
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
